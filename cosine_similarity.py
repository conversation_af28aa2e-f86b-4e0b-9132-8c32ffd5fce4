"""
余弦相似度计算模块

提供多种余弦相似度计算方法，支持不同类型的向量输入
"""

import numpy as np
from typing import List, Union, Tuple
import math


def cosine_similarity(vector1: Union[List, np.ndarray], vector2: Union[List, np.ndarray]) -> float:
    """
    计算两个向量的余弦相似度
    
    余弦相似度公式: cos(θ) = (A·B) / (|A|×|B|)
    其中 A·B 是向量的点积，|A| 和 |B| 是向量的模长
    
    Args:
        vector1: 第一个向量，可以是列表或numpy数组
        vector2: 第二个向量，可以是列表或numpy数组
    
    Returns:
        float: 余弦相似度值，范围在 [-1, 1] 之间
               1 表示完全相同方向
               0 表示正交（垂直）
               -1 表示完全相反方向
    
    Raises:
        ValueError: 当向量维度不匹配时
    """
    # 转换为numpy数组
    v1 = np.array(vector1, dtype=np.float64)
    v2 = np.array(vector2, dtype=np.float64)
    
    # 检查向量维度
    if v1.shape != v2.shape:
        raise ValueError(f"向量维度不匹配: {v1.shape} vs {v2.shape}")
    
    # 计算点积
    dot_product = np.dot(v1, v2)
    
    # 计算向量的模长（欧几里得范数）
    norm_v1 = np.linalg.norm(v1)
    norm_v2 = np.linalg.norm(v2)
    
    # 处理零向量的情况
    if norm_v1 == 0 or norm_v2 == 0:
        return 0.0
    
    # 计算余弦相似度
    similarity = dot_product / (norm_v1 * norm_v2)
    
    # 由于浮点数精度问题，确保结果在 [-1, 1] 范围内
    return np.clip(similarity, -1.0, 1.0)


def cosine_similarity_manual(vector1: Union[List, np.ndarray], vector2: Union[List, np.ndarray]) -> float:
    """
    手动实现的余弦相似度计算（不使用numpy的高级函数）
    
    Args:
        vector1: 第一个向量
        vector2: 第二个向量
    
    Returns:
        float: 余弦相似度值
    """
    v1 = list(vector1) if not isinstance(vector1, list) else vector1
    v2 = list(vector2) if not isinstance(vector2, list) else vector2
    
    if len(v1) != len(v2):
        raise ValueError(f"向量维度不匹配: {len(v1)} vs {len(v2)}")
    
    # 计算点积
    dot_product = sum(a * b for a, b in zip(v1, v2))
    
    # 计算向量的模长
    norm_v1 = math.sqrt(sum(a * a for a in v1))
    norm_v2 = math.sqrt(sum(b * b for b in v2))
    
    # 处理零向量
    if norm_v1 == 0 or norm_v2 == 0:
        return 0.0
    
    return dot_product / (norm_v1 * norm_v2)


def cosine_similarity_matrix(vectors: List[Union[List, np.ndarray]]) -> np.ndarray:
    """
    计算向量列表中所有向量对的余弦相似度矩阵
    
    Args:
        vectors: 向量列表
    
    Returns:
        numpy.ndarray: n×n 的相似度矩阵，其中 n 是向量的数量
                      matrix[i][j] 表示第i个向量和第j个向量的相似度
    """
    n = len(vectors)
    similarity_matrix = np.zeros((n, n))
    
    for i in range(n):
        for j in range(n):
            similarity_matrix[i][j] = cosine_similarity(vectors[i], vectors[j])
    
    return similarity_matrix


def find_most_similar(vectors: List[Union[List, np.ndarray]], 
                     names: List[str] = None) -> Tuple[int, int, float]:
    """
    找出向量列表中最相似的一对向量
    
    Args:
        vectors: 向量列表
        names: 向量名称列表（可选）
    
    Returns:
        tuple: (索引1, 索引2, 相似度值)
    """
    max_similarity = -1
    max_indices = (0, 0)
    
    for i in range(len(vectors)):
        for j in range(i + 1, len(vectors)):
            sim = cosine_similarity(vectors[i], vectors[j])
            if sim > max_similarity:
                max_similarity = sim
                max_indices = (i, j)
    
    return max_indices[0], max_indices[1], max_similarity


def cosine_distance(vector1: Union[List, np.ndarray], vector2: Union[List, np.ndarray]) -> float:
    """
    计算余弦距离（1 - 余弦相似度）
    
    余弦距离范围在 [0, 2] 之间，0 表示完全相同，2 表示完全相反
    
    Args:
        vector1: 第一个向量
        vector2: 第二个向量
    
    Returns:
        float: 余弦距离值
    """
    return 1 - cosine_similarity(vector1, vector2)


def batch_cosine_similarity(query_vector: Union[List, np.ndarray], 
                          vectors: List[Union[List, np.ndarray]]) -> List[float]:
    """
    计算查询向量与向量列表中每个向量的余弦相似度
    
    Args:
        query_vector: 查询向量
        vectors: 向量列表
    
    Returns:
        list: 相似度列表
    """
    similarities = []
    for vector in vectors:
        sim = cosine_similarity(query_vector, vector)
        similarities.append(sim)
    
    return similarities


if __name__ == "__main__":
    # 示例用法
    print("余弦相似度计算示例")
    print("=" * 50)
    
    # 示例向量
    vec1 = [1, 2, 3, 4]
    vec2 = [2, 4, 6, 8]  # vec1 的2倍，应该相似度为1
    vec3 = [-1, -2, -3, -4]  # vec1 的相反，应该相似度为-1
    vec4 = [1, 0, 0, 0]  # 与vec1正交的向量
    
    print(f"向量1: {vec1}")
    print(f"向量2: {vec2}")
    print(f"向量3: {vec3}")
    print(f"向量4: {vec4}")
    print()
    
    # 计算相似度
    print("相似度计算结果:")
    print(f"vec1 vs vec2: {cosine_similarity(vec1, vec2):.6f}")
    print(f"vec1 vs vec3: {cosine_similarity(vec1, vec3):.6f}")
    print(f"vec1 vs vec4: {cosine_similarity(vec1, vec4):.6f}")
    print()
    
    # 计算相似度矩阵
    vectors = [vec1, vec2, vec3, vec4]
    matrix = cosine_similarity_matrix(vectors)
    print("相似度矩阵:")
    print(matrix)
    print()
    
    # 找出最相似的向量对
    i, j, sim = find_most_similar(vectors)
    print(f"最相似的向量对: 向量{i+1} 和 向量{j+1}")
    print(f"相似度: {sim:.6f}")
