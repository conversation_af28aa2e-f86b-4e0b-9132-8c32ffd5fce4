def geohash4_neighbors(geohash):
    """获取 GeoHash4 的九宫格邻居"""
    
    # Base32 字符集
    base32 = "0123456789bcdefghjkmnpqrstuvwxyz"
    
    # 4位精度的边界和邻居映射表
    # 邻居字符映射表（基于您提供的正确数据）
    neighbors = {
        'top': {
            'odd': "238967debc01fg45kmstqrwxuvhjyznp",
            'even': "14365h7k9dcfesgujnmqp0r2twvyx8zb"
        },
        'right': {
            'odd': "14365h7k9dcfesgujnmqp0r2twvyx8zb", 
            'even': "238967debc01fg45kmstqrwxuvhjyznp"
        },
        'bottom': {
            'odd': "bc01fg45238967deuvhjyznpkmstqrwx",
            'even': "p0r21436x8zb9dcf5h7kjnmqesgutwvy"
        },
        'left': {
            'odd': "p0r21436x8zb9dcf5h7kjnmqesgutwvy",
            'even': "bc01fg45238967deuvhjyznpkmstqrwx"
        }
    }
    
    # 边界字符映射表
    borders = {
        'top': {
            'odd': "bcfguvyz",
            'even': "prxz"
        },
        'right': {
            'odd': "prxz",
            'even': "bcfguvyz"
        },
        'bottom': {
            'odd': "0145hjnp",
            'even': "028b"
        },
        'left': {
            'odd': "028b", 
            'even': "0145hjnp"
        }
    }
    
    def neighbor(geohash, direction):
        """计算指定方向的邻居"""
        if len(geohash) == 0:
            return None
            
        last_char = geohash[-1]
        parent = geohash[:-1]
        type_key = 'even' if len(geohash) % 2 == 0 else 'odd'
        
        # 检查边界
        if last_char in borders[direction][type_key]:
            parent = neighbor(parent, direction)
            if parent is None:
                return None
        
        # 获取邻居字符
        try:
            index = base32.index(last_char)
            return parent + neighbors[direction][type_key][index]
        except (ValueError, IndexError):
            return None
    
    # 计算九宫格
    center = geohash
    north = neighbor(center, 'top')
    south = neighbor(center, 'bottom')
    east = neighbor(center, 'right')
    west = neighbor(center, 'left')
    
    northeast = neighbor(north, 'right') if north else None
    northwest = neighbor(north, 'left') if north else None
    southeast = neighbor(south, 'right') if south else None
    southwest = neighbor(south, 'left') if south else None
    
    return [
        northwest, north, northeast,
        west, center, east,
        southwest, south, southeast
    ]
# 测试
geohash4 = "w7zq"
result = geohash4_neighbors(geohash4)
print("九宫格扩展结果:")
print(f"{result[0]} | {result[1]} | {result[2]}")
print(f"{result[3]} | {result[4]} | {result[5]}")
print(f"{result[6]} | {result[7]} | {result[8]}")
