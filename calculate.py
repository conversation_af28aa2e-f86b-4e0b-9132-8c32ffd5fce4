import json
import base64
import numpy as np
import pickle

def get_np(encoded_data):
    serialized_data = base64.b64decode(encoded_data)
    # 反序列化 pickle 对象
    encode_res = pickle.loads(serialized_data)
    restored_array = np.frombuffer(encode_res,dtype=np.float16).reshape(1024)
    return restored_array

with open("data.json", "r", encoding="utf-8") as f:
    data = json.load(f)
    list_map = data['output']

k_v = {}
array_list = []
for item in list_map:
    name = item.keys()[0]
    encoded_data = item.values()[0]
    restored_array = get_np(encoded_data)
    k_v[name] = restored_array
    array_list.append(restored_array)

print(k_v)
# similarity = util.cos_sim(array_list[0], array_list[1])
# print(similarity)