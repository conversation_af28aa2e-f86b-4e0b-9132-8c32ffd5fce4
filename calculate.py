import json
import base64
import numpy as np
import pickle
import math

def cosine_similarity(vector1, vector2):
    """
    计算两个向量的余弦相似度

    Args:
        vector1: 第一个向量 (numpy array)
        vector2: 第二个向量 (numpy array)

    Returns:
        float: 余弦相似度值，范围在 [-1, 1] 之间
               1 表示完全相同，0 表示正交，-1 表示完全相反
    """
    # 确保输入是 numpy 数组
    v1 = np.array(vector1)
    v2 = np.array(vector2)

    # 检查向量维度是否相同
    if v1.shape != v2.shape:
        raise ValueError(f"向量维度不匹配: {v1.shape} vs {v2.shape}")

    # 计算点积
    dot_product = np.dot(v1, v2)

    # 计算向量的模长
    norm_v1 = np.linalg.norm(v1)
    norm_v2 = np.linalg.norm(v2)

    # 避免除零错误
    if norm_v1 == 0 or norm_v2 == 0:
        return 0.0

    # 计算余弦相似度
    similarity = dot_product / (norm_v1 * norm_v2)

    return similarity


def cosine_similarity_batch(vectors):
    """
    计算向量列表中所有向量对的余弦相似度

    Args:
        vectors: 向量列表

    Returns:
        numpy array: 相似度矩阵
    """
    n = len(vectors)
    similarity_matrix = np.zeros((n, n))

    for i in range(n):
        for j in range(n):
            similarity_matrix[i][j] = cosine_similarity(vectors[i], vectors[j])

    return similarity_matrix


def get_np(encoded_data):
    serialized_data = base64.b64decode(encoded_data)
    # 反序列化 pickle 对象
    encode_res = pickle.loads(serialized_data)
    restored_array = np.frombuffer(encode_res,dtype=np.float32).reshape(1024)
    return restored_array

with open("data.json", "r", encoding="utf-8") as f:
    data = json.load(f)
    list_map = data['output']

k_v = {}
array_list = []
for item in list_map:
    for key, value in item.items():
        name = key
        encoded_data = value
        restored_array = get_np(encoded_data)
        k_v[name] = restored_array
        array_list.append(restored_array)

print("向量数据加载完成，共有", len(array_list), "个向量")
print("向量名称:", list(k_v.keys()))

# 计算前两个向量的余弦相似度
if len(array_list) >= 2:
    similarity = cosine_similarity(array_list[0], array_list[1])
    print(f"前两个向量的余弦相似度: {similarity:.6f}")

    # 计算所有向量对的相似度矩阵
    print("\n计算所有向量对的相似度矩阵...")
    similarity_matrix = cosine_similarity_batch(array_list)
    print("相似度矩阵:")
    print(similarity_matrix)

    # 找出最相似的向量对
    vector_names = list(k_v.keys())
    max_similarity = -1
    max_pair = None

    for i in range(len(array_list)):
        for j in range(i+1, len(array_list)):
            sim = similarity_matrix[i][j]
            if sim > max_similarity:
                max_similarity = sim
                max_pair = (vector_names[i], vector_names[j])

    if max_pair:
        print(f"\n最相似的向量对: {max_pair[0]} 和 {max_pair[1]}")
        print(f"相似度: {max_similarity:.6f}")
else:
    print("向量数量不足，无法计算相似度")