"""
余弦相似度算法使用示例

展示如何在不同场景下使用余弦相似度算法
"""

import numpy as np
from cosine_similarity import (
    cosine_similarity,
    cosine_similarity_matrix,
    find_most_similar,
    cosine_distance,
    batch_cosine_similarity
)


def example_1_basic_usage():
    """基础使用示例"""
    print("=" * 60)
    print("示例 1: 基础使用")
    print("=" * 60)
    
    # 定义两个向量
    vector_a = [1, 2, 3, 4, 5]
    vector_b = [2, 4, 6, 8, 10]  # vector_a 的2倍
    vector_c = [1, 0, 1, 0, 1]   # 不同的向量
    
    print(f"向量 A: {vector_a}")
    print(f"向量 B: {vector_b}")
    print(f"向量 C: {vector_c}")
    print()
    
    # 计算相似度
    sim_ab = cosine_similarity(vector_a, vector_b)
    sim_ac = cosine_similarity(vector_a, vector_c)
    sim_bc = cosine_similarity(vector_b, vector_c)
    
    print(f"A 和 B 的相似度: {sim_ab:.6f}")
    print(f"A 和 C 的相似度: {sim_ac:.6f}")
    print(f"B 和 C 的相似度: {sim_bc:.6f}")
    print()


def example_2_text_similarity():
    """文本相似度示例（使用模拟的词向量）"""
    print("=" * 60)
    print("示例 2: 文本相似度分析")
    print("=" * 60)
    
    # 模拟的文档词向量（实际应用中可能来自 Word2Vec, BERT 等）
    documents = {
        "Python编程教程": [0.8, 0.9, 0.1, 0.2, 0.7, 0.3],
        "Java编程指南": [0.7, 0.8, 0.2, 0.3, 0.6, 0.4],
        "机器学习入门": [0.9, 0.2, 0.8, 0.7, 0.1, 0.6],
        "深度学习实战": [0.8, 0.1, 0.9, 0.8, 0.2, 0.7],
        "美食制作方法": [0.1, 0.2, 0.1, 0.1, 0.8, 0.9]
    }
    
    doc_names = list(documents.keys())
    doc_vectors = list(documents.values())
    
    print("文档列表:")
    for i, name in enumerate(doc_names):
        print(f"  {i+1}. {name}")
    print()
    
    # 计算相似度矩阵
    similarity_matrix = cosine_similarity_matrix(doc_vectors)
    
    print("文档相似度矩阵:")
    print("     ", end="")
    for i in range(len(doc_names)):
        print(f"Doc{i+1:2d}", end="  ")
    print()
    
    for i in range(len(doc_names)):
        print(f"Doc{i+1:2d}", end="  ")
        for j in range(len(doc_names)):
            print(f"{similarity_matrix[i][j]:.3f}", end="  ")
        print()
    print()
    
    # 找出最相似的文档对
    i, j, max_sim = find_most_similar(doc_vectors)
    print(f"最相似的文档对:")
    print(f"  '{doc_names[i]}' 和 '{doc_names[j]}'")
    print(f"  相似度: {max_sim:.6f}")
    print()


def example_3_recommendation_system():
    """推荐系统示例"""
    print("=" * 60)
    print("示例 3: 推荐系统")
    print("=" * 60)
    
    # 用户对不同类型电影的评分 [动作, 喜剧, 爱情, 科幻, 恐怖]
    user_ratings = {
        "Alice": [5, 3, 2, 4, 1],
        "Bob": [4, 2, 1, 5, 2],
        "Charlie": [2, 5, 4, 1, 1],
        "Diana": [1, 4, 5, 2, 1],
        "Eve": [5, 1, 1, 4, 3]
    }
    
    print("用户电影评分 [动作, 喜剧, 爱情, 科幻, 恐怖]:")
    for user, ratings in user_ratings.items():
        print(f"  {user:8s}: {ratings}")
    print()
    
    # 为 Alice 找相似用户
    target_user = "Alice"
    target_ratings = user_ratings[target_user]
    
    other_users = [user for user in user_ratings.keys() if user != target_user]
    other_ratings = [user_ratings[user] for user in other_users]
    
    similarities = batch_cosine_similarity(target_ratings, other_ratings)
    
    print(f"与 {target_user} 最相似的用户:")
    user_similarities = list(zip(other_users, similarities))
    user_similarities.sort(key=lambda x: x[1], reverse=True)
    
    for user, sim in user_similarities:
        print(f"  {user:8s}: {sim:.6f}")
    print()
    
    # 推荐逻辑：找到最相似的用户，推荐他们喜欢但目标用户评分较低的电影
    most_similar_user = user_similarities[0][0]
    most_similar_ratings = user_ratings[most_similar_user]
    
    movie_types = ["动作", "喜剧", "爱情", "科幻", "恐怖"]
    print(f"基于 {most_similar_user} 的偏好为 {target_user} 推荐:")
    
    for i, (movie_type, alice_rating, similar_rating) in enumerate(
        zip(movie_types, target_ratings, most_similar_ratings)
    ):
        if similar_rating > alice_rating and similar_rating >= 4:
            print(f"  推荐 {movie_type} 电影 (相似用户评分: {similar_rating}, 你的评分: {alice_rating})")


def example_4_image_similarity():
    """图像相似度示例（使用模拟的图像特征向量）"""
    print("=" * 60)
    print("示例 4: 图像相似度分析")
    print("=" * 60)
    
    # 模拟的图像特征向量（实际应用中可能来自 CNN 特征提取）
    np.random.seed(42)  # 确保结果可重现
    
    images = {
        "猫咪1": np.random.rand(128) * 0.5 + np.array([1]*64 + [0]*64),  # 偏向前64维
        "猫咪2": np.random.rand(128) * 0.5 + np.array([1]*64 + [0]*64),  # 类似猫咪1
        "狗狗1": np.random.rand(128) * 0.5 + np.array([0]*64 + [1]*64),  # 偏向后64维
        "狗狗2": np.random.rand(128) * 0.5 + np.array([0]*64 + [1]*64),  # 类似狗狗1
        "风景": np.random.rand(128) * 0.3 + 0.5,  # 均匀分布
    }
    
    image_names = list(images.keys())
    image_vectors = list(images.values())
    
    print("图像列表:")
    for i, name in enumerate(image_names):
        print(f"  {i+1}. {name}")
    print()
    
    # 计算相似度矩阵
    similarity_matrix = cosine_similarity_matrix(image_vectors)
    
    print("图像相似度矩阵:")
    print("        ", end="")
    for name in image_names:
        print(f"{name:8s}", end=" ")
    print()
    
    for i, name in enumerate(image_names):
        print(f"{name:8s}", end=" ")
        for j in range(len(image_names)):
            print(f"{similarity_matrix[i][j]:.6f}", end=" ")
        print()
    print()
    
    # 图像检索示例
    query_image = "猫咪1"
    query_vector = images[query_image]
    
    other_images = [name for name in image_names if name != query_image]
    other_vectors = [images[name] for name in other_images]
    
    similarities = batch_cosine_similarity(query_vector, other_vectors)
    
    print(f"与 '{query_image}' 最相似的图像:")
    image_similarities = list(zip(other_images, similarities))
    image_similarities.sort(key=lambda x: x[1], reverse=True)
    
    for image, sim in image_similarities:
        print(f"  {image:8s}: {sim:.6f}")


def example_5_distance_vs_similarity():
    """余弦距离 vs 余弦相似度"""
    print("=" * 60)
    print("示例 5: 余弦距离 vs 余弦相似度")
    print("=" * 60)
    
    vectors = {
        "向量A": [1, 0, 0],
        "向量B": [0, 1, 0],      # 与A正交
        "向量C": [1, 1, 0],      # 与A成45度角
        "向量D": [-1, 0, 0],     # 与A相反
    }
    
    base_vector = vectors["向量A"]
    
    print("基准向量A: [1, 0, 0]")
    print()
    print("向量名称    向量值      相似度    距离")
    print("-" * 45)
    
    for name, vector in vectors.items():
        if name != "向量A":
            similarity = cosine_similarity(base_vector, vector)
            distance = cosine_distance(base_vector, vector)
            print(f"{name:8s}  {str(vector):12s}  {similarity:8.3f}  {distance:6.3f}")


if __name__ == "__main__":
    print("余弦相似度算法应用示例")
    print("作者: AI助手")
    print("日期: 2024")
    print()
    
    # 运行所有示例
    example_1_basic_usage()
    example_2_text_similarity()
    example_3_recommendation_system()
    example_4_image_similarity()
    example_5_distance_vs_similarity()
    
    print("\n" + "=" * 60)
    print("所有示例运行完成！")
    print("=" * 60)
