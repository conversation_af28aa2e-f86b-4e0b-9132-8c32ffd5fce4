import math

class GeoHash4:
    BASE32 = "0123456789bcdefghjkmnpqrstuvwxyz"
    
    @staticmethod
    def encode(lat, lon, precision=4):
        """
        编码经纬度为 GeoHash
        :param lat: 纬度 (-90, 90)
        :param lon: 经度 (-180, 180)
        :param precision: 精度，默认4位
        :return: GeoHash 字符串
        """
        lat_range = [-90.0, 90.0]
        lon_range = [-180.0, 180.0]
        
        geohash = []
        bits = 0
        bit = 0
        even_bit = True
        
        while len(geohash) < precision:
            if even_bit:  # 经度
                mid = (lon_range[0] + lon_range[1]) / 2
                if lon >= mid:
                    bit = (bit << 1) | 1
                    lon_range[0] = mid
                else:
                    bit = bit << 1
                    lon_range[1] = mid
            else:  # 纬度
                mid = (lat_range[0] + lat_range[1]) / 2
                if lat >= mid:
                    bit = (bit << 1) | 1
                    lat_range[0] = mid
                else:
                    bit = bit << 1
                    lat_range[1] = mid
            
            even_bit = not even_bit
            bits += 1
            
            if bits == 5:
                geohash.append(GeoHash4.BASE32[bit])
                bits = 0
                bit = 0
        
        return ''.join(geohash)
    
    @staticmethod
    def decode(geohash):
        """
        解码 GeoHash 为经纬度范围
        :param geohash: GeoHash 字符串
        :return: (lat_range, lon_range)
        """
        lat_range = [-90.0, 90.0]
        lon_range = [-180.0, 180.0]
        
        even_bit = True
        
        for c in geohash:
            idx = GeoHash4.BASE32.index(c)
            for i in range(4, -1, -1):
                bit = (idx >> i) & 1
                if even_bit:  # 经度
                    mid = (lon_range[0] + lon_range[1]) / 2
                    if bit == 1:
                        lon_range[0] = mid
                    else:
                        lon_range[1] = mid
                else:  # 纬度
                    mid = (lat_range[0] + lat_range[1]) / 2
                    if bit == 1:
                        lat_range[0] = mid
                    else:
                        lat_range[1] = mid
                even_bit = not even_bit
        
        return lat_range, lon_range
    
    @staticmethod
    def get_center(geohash):
        """获取 GeoHash 中心点坐标"""
        lat_range, lon_range = GeoHash4.decode(geohash)
        lat = (lat_range[0] + lat_range[1]) / 2
        lon = (lon_range[0] + lon_range[1]) / 2
        return lat, lon

# 使用示例
if __name__ == "__main__":
    # 北京天安门坐标
    lat, lon = 41.881873, 123.396578
    
    # 编码为4位 GeoHash
    geohash = GeoHash4.encode(lat, lon, 4)
    print(f"坐标 ({lat}, {lon}) 的 GeoHash4: {geohash}")
    
    # 解码
    lat_range, lon_range = GeoHash4.decode(geohash)
    print(f"GeoHash {geohash} 的范围:")
    print(f"纬度: {lat_range}")
    print(f"经度: {lon_range}")
    
    # 获取中心点
    center_lat, center_lon = GeoHash4.get_center(geohash)
    print(f"中心点: ({center_lat}, {center_lon})")
