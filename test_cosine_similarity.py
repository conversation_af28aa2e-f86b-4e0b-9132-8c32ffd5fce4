"""
余弦相似度算法测试文件
"""

import numpy as np
import unittest
from cosine_similarity import (
    cosine_similarity, 
    cosine_similarity_manual,
    cosine_similarity_matrix,
    find_most_similar,
    cosine_distance,
    batch_cosine_similarity
)


class TestCosineSimilarity(unittest.TestCase):
    
    def setUp(self):
        """设置测试数据"""
        self.vec1 = [1, 2, 3]
        self.vec2 = [2, 4, 6]  # vec1 的2倍
        self.vec3 = [-1, -2, -3]  # vec1 的相反
        self.vec4 = [1, 0, 0]  # 与vec1不完全正交
        self.zero_vec = [0, 0, 0]  # 零向量
        
    def test_identical_vectors(self):
        """测试相同向量的相似度应该为1"""
        result = cosine_similarity(self.vec1, self.vec1)
        self.assertAlmostEqual(result, 1.0, places=6)
        
    def test_proportional_vectors(self):
        """测试成比例向量的相似度应该为1"""
        result = cosine_similarity(self.vec1, self.vec2)
        self.assertAlmostEqual(result, 1.0, places=6)
        
    def test_opposite_vectors(self):
        """测试相反向量的相似度应该为-1"""
        result = cosine_similarity(self.vec1, self.vec3)
        self.assertAlmostEqual(result, -1.0, places=6)
        
    def test_zero_vector(self):
        """测试零向量的相似度应该为0"""
        result = cosine_similarity(self.vec1, self.zero_vec)
        self.assertEqual(result, 0.0)
        
    def test_manual_vs_numpy(self):
        """测试手动实现和numpy实现的结果一致性"""
        result1 = cosine_similarity(self.vec1, self.vec4)
        result2 = cosine_similarity_manual(self.vec1, self.vec4)
        self.assertAlmostEqual(result1, result2, places=6)
        
    def test_dimension_mismatch(self):
        """测试维度不匹配时应该抛出异常"""
        vec_2d = [1, 2]
        with self.assertRaises(ValueError):
            cosine_similarity(self.vec1, vec_2d)
            
    def test_similarity_matrix(self):
        """测试相似度矩阵计算"""
        vectors = [self.vec1, self.vec2, self.vec3]
        matrix = cosine_similarity_matrix(vectors)
        
        # 检查矩阵形状
        self.assertEqual(matrix.shape, (3, 3))
        
        # 检查对角线元素为1（自己与自己的相似度）
        for i in range(3):
            self.assertAlmostEqual(matrix[i][i], 1.0, places=6)
            
        # 检查矩阵对称性
        for i in range(3):
            for j in range(3):
                self.assertAlmostEqual(matrix[i][j], matrix[j][i], places=6)
                
    def test_find_most_similar(self):
        """测试找出最相似向量对"""
        vectors = [self.vec1, self.vec3, self.vec2]  # vec1和vec2最相似
        i, j, sim = find_most_similar(vectors)
        
        # vec1 (索引0) 和 vec2 (索引2) 应该是最相似的
        self.assertTrue((i == 0 and j == 2) or (i == 2 and j == 0))
        self.assertAlmostEqual(sim, 1.0, places=6)
        
    def test_cosine_distance(self):
        """测试余弦距离计算"""
        distance = cosine_distance(self.vec1, self.vec2)
        self.assertAlmostEqual(distance, 0.0, places=6)  # 相同方向，距离为0
        
        distance = cosine_distance(self.vec1, self.vec3)
        self.assertAlmostEqual(distance, 2.0, places=6)  # 相反方向，距离为2
        
    def test_batch_similarity(self):
        """测试批量相似度计算"""
        vectors = [self.vec2, self.vec3, self.vec4]
        similarities = batch_cosine_similarity(self.vec1, vectors)
        
        self.assertEqual(len(similarities), 3)
        self.assertAlmostEqual(similarities[0], 1.0, places=6)  # vec1 vs vec2
        self.assertAlmostEqual(similarities[1], -1.0, places=6)  # vec1 vs vec3


def run_performance_test():
    """性能测试"""
    print("\n性能测试")
    print("=" * 50)
    
    import time
    
    # 生成随机向量
    np.random.seed(42)
    vectors = [np.random.rand(1000) for _ in range(100)]
    
    # 测试单次计算时间
    start_time = time.time()
    for i in range(100):
        cosine_similarity(vectors[0], vectors[i])
    end_time = time.time()
    
    print(f"100次单向量相似度计算耗时: {end_time - start_time:.4f} 秒")
    
    # 测试矩阵计算时间
    start_time = time.time()
    matrix = cosine_similarity_matrix(vectors[:10])  # 只测试前10个向量
    end_time = time.time()
    
    print(f"10×10相似度矩阵计算耗时: {end_time - start_time:.4f} 秒")


def run_examples():
    """运行示例"""
    print("\n实际应用示例")
    print("=" * 50)
    
    # 文本向量化示例（模拟）
    print("1. 文本相似度示例（模拟向量）:")
    text_vectors = {
        "机器学习很有趣": [0.8, 0.6, 0.9, 0.2, 0.1],
        "深度学习很有趣": [0.7, 0.5, 0.8, 0.3, 0.2],
        "今天天气很好": [0.1, 0.2, 0.1, 0.9, 0.8],
        "人工智能发展": [0.9, 0.7, 0.6, 0.1, 0.0]
    }
    
    texts = list(text_vectors.keys())
    vectors = list(text_vectors.values())
    
    print("文本列表:")
    for i, text in enumerate(texts):
        print(f"  {i+1}. {text}")
    
    print("\n相似度矩阵:")
    matrix = cosine_similarity_matrix(vectors)
    for i in range(len(texts)):
        for j in range(len(texts)):
            print(f"{matrix[i][j]:.3f}", end="  ")
        print()
    
    # 找出最相似的文本对
    i, j, sim = find_most_similar(vectors)
    print(f"\n最相似的文本对:")
    print(f"'{texts[i]}' 和 '{texts[j]}'")
    print(f"相似度: {sim:.6f}")
    
    print("\n2. 推荐系统示例:")
    # 用户偏好向量示例
    user_preferences = {
        "用户A": [5, 3, 0, 1, 4],  # 喜欢动作片和科幻片
        "用户B": [4, 2, 1, 0, 5],  # 喜欢动作片和科幻片
        "用户C": [1, 5, 4, 3, 0],  # 喜欢爱情片和喜剧片
        "用户D": [0, 4, 5, 2, 1]   # 喜欢爱情片和喜剧片
    }
    
    users = list(user_preferences.keys())
    preferences = list(user_preferences.values())
    
    print("用户偏好向量 [动作, 爱情, 喜剧, 恐怖, 科幻]:")
    for user, pref in user_preferences.items():
        print(f"  {user}: {pref}")
    
    # 为用户A找相似用户
    query_user = "用户A"
    query_vector = user_preferences[query_user]
    other_users = [user for user in users if user != query_user]
    other_preferences = [user_preferences[user] for user in other_users]
    
    similarities = batch_cosine_similarity(query_vector, other_preferences)
    
    print(f"\n与{query_user}最相似的用户:")
    for i, (user, sim) in enumerate(zip(other_users, similarities)):
        print(f"  {user}: {sim:.6f}")


if __name__ == "__main__":
    # 运行单元测试
    print("运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行性能测试
    run_performance_test()
    
    # 运行示例
    run_examples()
