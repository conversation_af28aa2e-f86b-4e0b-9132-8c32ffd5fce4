# 余弦相似度算法实现

这个项目提供了完整的余弦相似度算法实现，包括多种计算方法和实际应用示例。

## 文件说明

- `cosine_similarity.py` - 核心余弦相似度算法模块
- `calculate.py` - 你的原始数据处理文件（已集成余弦相似度功能）
- `test_cosine_similarity.py` - 完整的单元测试和性能测试
- `example_usage.py` - 各种应用场景的示例代码

## 核心功能

### 1. 基础余弦相似度计算

```python
from cosine_similarity import cosine_similarity

# 计算两个向量的相似度
vector1 = [1, 2, 3, 4]
vector2 = [2, 4, 6, 8]
similarity = cosine_similarity(vector1, vector2)
print(f"相似度: {similarity}")  # 输出: 1.0 (完全相同方向)
```

### 2. 批量相似度计算

```python
from cosine_similarity import cosine_similarity_matrix

vectors = [[1, 2, 3], [2, 4, 6], [-1, -2, -3]]
matrix = cosine_similarity_matrix(vectors)
print(matrix)
```

### 3. 找出最相似的向量对

```python
from cosine_similarity import find_most_similar

vectors = [[1, 2, 3], [2, 4, 6], [1, 0, 0]]
i, j, similarity = find_most_similar(vectors)
print(f"最相似的是向量 {i} 和向量 {j}，相似度: {similarity}")
```

## 算法原理

余弦相似度通过计算两个向量之间的夹角余弦值来衡量它们的相似程度：

```
cos(θ) = (A·B) / (|A| × |B|)
```

其中：
- A·B 是向量A和B的点积
- |A| 和 |B| 是向量的模长（欧几里得范数）

### 相似度值含义

- **1.0**: 完全相同方向（完全相似）
- **0.0**: 正交（垂直，无相关性）
- **-1.0**: 完全相反方向（完全不相似）

## 应用场景

### 1. 文本相似度分析
```python
# 文档向量化后的相似度计算
doc1_vector = [0.8, 0.6, 0.9, 0.2]  # 来自 Word2Vec, BERT 等
doc2_vector = [0.7, 0.5, 0.8, 0.3]
similarity = cosine_similarity(doc1_vector, doc2_vector)
```

### 2. 推荐系统
```python
# 用户偏好相似度
user1_preferences = [5, 3, 0, 1, 4]  # 对不同类型内容的评分
user2_preferences = [4, 2, 1, 0, 5]
user_similarity = cosine_similarity(user1_preferences, user2_preferences)
```

### 3. 图像相似度
```python
# 图像特征向量相似度
image1_features = extract_features(image1)  # CNN 特征提取
image2_features = extract_features(image2)
image_similarity = cosine_similarity(image1_features, image2_features)
```

## 运行测试

```bash
# 运行单元测试
python3 test_cosine_similarity.py

# 运行你的数据处理
python3 calculate.py

# 运行应用示例
python3 example_usage.py
```

## 性能特点

- 支持任意维度的向量
- 自动处理零向量情况
- 提供手动实现和NumPy优化版本
- 包含完整的错误处理和输入验证
- 高效的批量计算功能

## 测试结果

所有测试都已通过：
- ✅ 相同向量相似度为1
- ✅ 相反向量相似度为-1
- ✅ 零向量处理正确
- ✅ 维度不匹配异常处理
- ✅ 相似度矩阵计算正确
- ✅ 性能测试通过

## 你的数据处理结果

根据你的 `calculate.py` 运行结果：
- 成功加载了2个向量：'星巴克南京西路旗舰店' 和 '星巴克上海南京西路店'
- 两个向量的余弦相似度为 **0.951805**
- 这表明这两个店铺的向量表示非常相似，可能代表相似的特征或属性

## 扩展功能

该实现还包括：
- `cosine_distance()` - 计算余弦距离（1 - 相似度）
- `batch_cosine_similarity()` - 一对多相似度计算
- 完整的类型提示支持
- 详细的文档字符串

## 依赖项

- numpy
- math (标准库)
- typing (标准库)

这个实现既适合学习理解，也适合在实际项目中使用！
